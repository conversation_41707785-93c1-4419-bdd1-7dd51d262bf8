// نموذج الشخصية
class Character {
  final String id;
  final String name;
  final String description;
  final Map<String, String> expressions; // مختلف تعبيرات الوجه
  final String defaultExpression;
  
  Character({
    required this.id,
    required this.name,
    required this.description,
    required this.expressions,
    required this.defaultExpression,
  });
  
  String getExpressionPath(String expression) {
    return expressions[expression] ?? expressions[defaultExpression]!;
  }
}

// الشخصيات المحددة مسبقاً
class GameCharacters {
  static final Character bariq = Character(
    id: 'bariq',
    name: 'بارق',
    description: 'مهندس معماري، 27 سنة',
    expressions: {
      'normal': 'assets/images/characters/bariq_normal.png',
      'surprised': 'assets/images/characters/bariq_surprised.png',
      'determined': 'assets/images/characters/bariq_determined.png',
      'worried': 'assets/images/characters/bariq_worried.png',
      'happy': 'assets/images/characters/bariq_happy.png',
    },
    defaultExpression: 'normal',
  );
  
  static final Character layla = Character(
    id: 'layla',
    name: 'ليلى',
    description: 'أستاذة تاريخ، 25 سنة',
    expressions: {
      'normal': 'assets/images/characters/layla_normal.png',
      'smiling': 'assets/images/characters/layla_smiling.png',
      'serious': 'assets/images/characters/layla_serious.png',
      'worried': 'assets/images/characters/layla_worried.png',
      'determined': 'assets/images/characters/layla_determined.png',
    },
    defaultExpression: 'normal',
  );
  
  static final Character kamal = Character(
    id: 'kamal',
    name: 'الأستاذ كمال',
    description: 'أستاذ تاريخ متقاعد، 60 سنة',
    expressions: {
      'normal': 'assets/images/characters/kamal_normal.png',
      'wise': 'assets/images/characters/kamal_wise.png',
      'concerned': 'assets/images/characters/kamal_concerned.png',
    },
    defaultExpression: 'normal',
  );
  
  static final Character rashid = Character(
    id: 'rashid',
    name: 'رشيد',
    description: 'تاجر آثار، 35 سنة',
    expressions: {
      'normal': 'assets/images/characters/rashid_normal.png',
      'evil': 'assets/images/characters/rashid_evil.png',
      'angry': 'assets/images/characters/rashid_angry.png',
    },
    defaultExpression: 'normal',
  );
  
  static final Character fatima = Character(
    id: 'fatima',
    name: 'الحاجة فاطمة',
    description: 'جارة قديمة، 75 سنة',
    expressions: {
      'normal': 'assets/images/characters/fatima_normal.png',
      'smiling': 'assets/images/characters/fatima_smiling.png',
    },
    defaultExpression: 'normal',
  );
  
  static Map<String, Character> getAllCharacters() {
    return {
      'bariq': bariq,
      'layla': layla,
      'kamal': kamal,
      'rashid': rashid,
      'fatima': fatima,
    };
  }
}
