import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';

class TextBoxWidget extends StatelessWidget {
  final String? speaker;
  final String text;
  final bool isAnimating;
  final VoidCallback? onTap;

  const TextBoxWidget({
    super.key,
    this.speaker,
    required this.text,
    required this.isAnimating,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        margin: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(15.r),
          border: Border.all(
            color: Colors.amber.withValues(alpha: 0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.5),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Padding(
          padding: EdgeInsets.all(20.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              // Speaker name
              if (speaker != null) ...[
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 12.w,
                    vertical: 6.h,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(15.r),
                    border: Border.all(
                      color: Colors.amber.withValues(alpha: 0.5),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    speaker!,
                    style: TextStyle(
                      color: Colors.amber,
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                SizedBox(height: 12.h),
              ],

              // Text content
              Text(
                text,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 16.sp,
                  height: 1.5,
                  fontFamily: 'NotoSansArabic',
                ),
                textAlign: TextAlign.right,
              ),

              // Continue indicator
              if (!isAnimating) ...[
                SizedBox(height: 12.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Icon(
                      Icons.touch_app,
                      color: Colors.amber.withValues(alpha: 0.7),
                      size: 20.sp,
                    ).animate(
                      onPlay: (controller) => controller.repeat(),
                    ).fadeIn(duration: 1000.ms).then().fadeOut(duration: 1000.ms),
                    SizedBox(width: 8.w),
                    Text(
                      'اضغط للمتابعة',
                      style: TextStyle(
                        color: Colors.amber.withValues(alpha: 0.7),
                        fontSize: 12.sp,
                        fontStyle: FontStyle.italic,
                      ),
                    ).animate(
                      onPlay: (controller) => controller.repeat(),
                    ).fadeIn(duration: 1000.ms).then().fadeOut(duration: 1000.ms),
                  ],
                ),
              ],
            ],
          ),
        ),
      ).animate().slideY(begin: 1.0, duration: 500.ms).fadeIn(),
    );
  }
}
