import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';

class AboutScreen extends StatelessWidget {
  const AboutScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('حول اللعبة'),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(20.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Game Logo/Title
            Container(
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.amber.withValues(alpha: 0.2),
                    Colors.amber.withValues(alpha: 0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(20.r),
                border: Border.all(
                  color: Colors.amber.withValues(alpha: 0.5),
                  width: 2,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.auto_stories,
                    size: 80.sp,
                    color: Colors.amber,
                  ).animate().scale(duration: 1000.ms),

                  SizedBox(height: 16.h),

                  Text(
                    'أسرار الجزائر العتيقة',
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(delay: 300.ms),

                  SizedBox(height: 8.h),

                  Text(
                    'رواية مرئية تفاعلية',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.white70,
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(delay: 500.ms),
                ],
              ),
            ),

            SizedBox(height: 32.h),

            // Game Description
            _buildInfoSection(
              'وصف اللعبة',
              'انطلق في رحلة مشوقة مع بارق، مهندس معماري شاب يعود إلى مدينة قسنطينة لاكتشاف أسرار عائلته العريقة. '
              'اكتشف كنوزاً مخفية من العهد العثماني، وحل ألغازاً تاريخية، واتخذ قرارات صعبة تؤثر على مسار القصة. '
              'لعبة تحتفي بالتراث الجزائري والثقافة العربية الإسلامية.',
              Icons.description,
              delay: 700,
            ),

            SizedBox(height: 24.h),

            // Features
            _buildInfoSection(
              'مميزات اللعبة',
              '• قصة تفاعلية مع خيارات متعددة\n'
              '• شخصيات عميقة ومتطورة\n'
              '• رسوم وموسيقى عالية الجودة\n'
              '• نظام حفظ متقدم\n'
              '• نهايات متعددة حسب اختياراتك\n'
              '• محتوى يحترم القيم الإسلامية',
              Icons.star,
              delay: 900,
            ),

            SizedBox(height: 24.h),

            // Developer Info
            _buildInfoSection(
              'المطور',
              'تم تطوير هذه اللعبة بواسطة شايبي وائل باستخدام تقنيات Flutter المتقدمة. '
              'اللعبة مصممة خصيصاً للجمهور العربي مع التركيز على الجودة والأصالة.',
              Icons.person,
              delay: 1100,
            ),

            SizedBox(height: 32.h),

            // Copyright
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                color: const Color(0xFF16213e),
                borderRadius: BorderRadius.circular(12.r),
                border: Border.all(
                  color: Colors.amber.withValues(alpha: 0.3),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Text(
                    '© 2025 شايبي وائل',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.amber,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 8.h),

                  Text(
                    'جميع الحقوق محفوظة',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 16.h),

                  Text(
                    'الإصدار 1.0.0',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: Colors.white38,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ).animate().fadeIn(delay: 1300.ms).slideY(begin: 0.3),

            SizedBox(height: 32.h),

            // Thank you message
            Container(
              padding: EdgeInsets.all(20.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.green.withOpacity(0.2),
                    Colors.green.withOpacity(0.1),
                  ],
                ),
                borderRadius: BorderRadius.circular(15.r),
                border: Border.all(
                  color: Colors.green.withOpacity(0.5),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    Icons.favorite,
                    color: Colors.red,
                    size: 32.sp,
                  ),

                  SizedBox(height: 12.h),

                  Text(
                    'شكراً لك على لعب أسرار الجزائر العتيقة!',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 8.h),

                  Text(
                    'نتمنى أن تستمتع بهذه التجربة الفريدة',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ).animate().fadeIn(delay: 1500.ms).scale(begin: Offset(0.8, 0.8)),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoSection(
    String title,
    String content,
    IconData icon,
    {int delay = 0}
  ) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(20.w),
      decoration: BoxDecoration(
        color: const Color(0xFF16213e),
        borderRadius: BorderRadius.circular(15.r),
        border: Border.all(
          color: Colors.amber.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                icon,
                color: Colors.amber,
                size: 24.sp,
              ),
              SizedBox(width: 12.w),
              Text(
                title,
                style: TextStyle(
                  fontSize: 18.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.amber,
                ),
              ),
            ],
          ),

          SizedBox(height: 12.h),

          Text(
            content,
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.white,
              height: 1.6,
            ),
            textAlign: TextAlign.right,
          ),
        ],
      ),
    ).animate().fadeIn(delay: delay.ms, duration: 800.ms).slideX(begin: -0.3);
  }
}
