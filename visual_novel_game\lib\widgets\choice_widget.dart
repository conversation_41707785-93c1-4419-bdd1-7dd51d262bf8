import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/scene.dart';

class ChoiceWidget extends StatefulWidget {
  final Choice choice;
  final VoidCallback onTap;

  const ChoiceWidget({
    super.key,
    required this.choice,
    required this.onTap,
  });

  @override
  State<ChoiceWidget> createState() => _ChoiceWidgetState();
}

class _ChoiceWidgetState extends State<ChoiceWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      onTapDown: (_) => setState(() => _isHovered = true),
      onTapUp: (_) => setState(() => _isHovered = false),
      onTapCancel: () => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        padding: EdgeInsets.symmetric(
          horizontal: 20.w,
          vertical: 16.h,
        ),
        decoration: BoxDecoration(
          color: _isHovered 
              ? Colors.amber.withOpacity(0.3)
              : Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(12.r),
          border: Border.all(
            color: _isHovered 
                ? Colors.amber
                : Colors.amber.withOpacity(0.5),
            width: _isHovered ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: _isHovered 
                  ? Colors.amber.withOpacity(0.3)
                  : Colors.black.withOpacity(0.3),
              blurRadius: _isHovered ? 8 : 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          children: [
            // Choice indicator
            Container(
              width: 8.w,
              height: 8.w,
              decoration: BoxDecoration(
                color: Colors.amber,
                shape: BoxShape.circle,
              ),
            ),
            
            SizedBox(width: 12.w),
            
            // Choice text
            Expanded(
              child: Text(
                widget.choice.text,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 15.sp,
                  fontWeight: _isHovered ? FontWeight.w600 : FontWeight.normal,
                  height: 1.4,
                ),
                textAlign: TextAlign.right,
              ),
            ),
            
            // Arrow indicator
            Icon(
              Icons.arrow_forward_ios,
              color: Colors.amber.withOpacity(0.7),
              size: 16.sp,
            ),
          ],
        ),
      ).animate().fadeIn(duration: 300.ms).slideX(begin: 0.3),
    );
  }
}
