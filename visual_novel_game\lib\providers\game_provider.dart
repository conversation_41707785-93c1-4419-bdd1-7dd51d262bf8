import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/game_state.dart';
import '../models/scene.dart';
import '../services/audio_service.dart';
import '../data/game_data.dart';

class GameProvider extends ChangeNotifier {
  GameState _gameState = GameState.newGame();
  Scene? _currentScene;
  bool _isLoading = false;
  bool _isTextAnimating = false;
  String _displayedText = '';
  int _textSpeed = 50; // milliseconds per character

  // Audio service
  final AudioService _audioService = AudioService();

  // Getters
  GameState get gameState => _gameState;
  Scene? get currentScene => _currentScene;
  bool get isLoading => _isLoading;
  bool get isTextAnimating => _isTextAnimating;
  String get displayedText => _displayedText;
  int get textSpeed => _textSpeed;

  // Settings
  double _musicVolume = 0.7;
  double _soundVolume = 0.8;
  bool _autoSave = true;

  double get musicVolume => _musicVolume;
  double get soundVolume => _soundVolume;
  bool get autoSave => _autoSave;

  // Initialize game
  Future<void> initializeGame() async {
    _isLoading = true;
    notifyListeners();

    try {
      await _loadSettings();
      await _loadCurrentScene();
    } catch (e) {
      debugPrint('Error initializing game: $e');
    }

    _isLoading = false;
    notifyListeners();
  }

  // Load current scene
  Future<void> _loadCurrentScene() async {
    try {
      _currentScene = GameData.getScene(_gameState.currentSceneId);
      if (_currentScene != null) {
        await _playSceneAudio();
        _startTextAnimation(_currentScene!.text);
      }
    } catch (e) {
      debugPrint('Error loading scene: $e');
    }
  }

  // Play scene audio
  Future<void> _playSceneAudio() async {
    if (_currentScene?.music != null) {
      await _audioService.playMusic(_currentScene!.music!, volume: _musicVolume);
    }
    if (_currentScene?.soundEffect != null) {
      await _audioService.playSound(_currentScene!.soundEffect!, volume: _soundVolume);
    }
  }

  // Text animation
  void _startTextAnimation(String text) {
    _isTextAnimating = true;
    _displayedText = '';
    notifyListeners();

    int currentIndex = 0;

    void animateNextCharacter() {
      if (currentIndex < text.length) {
        _displayedText += text[currentIndex];
        currentIndex++;
        notifyListeners();

        Future.delayed(Duration(milliseconds: _textSpeed), animateNextCharacter);
      } else {
        _isTextAnimating = false;
        notifyListeners();
      }
    }

    animateNextCharacter();
  }

  // Skip text animation
  void skipTextAnimation() {
    if (_isTextAnimating && _currentScene != null) {
      _displayedText = _currentScene!.text;
      _isTextAnimating = false;
      notifyListeners();
    }
  }

  // Make choice
  Future<void> makeChoice(Choice choice) async {
    if (_isTextAnimating) return;

    // Update stats if choice has stat changes
    if (choice.statChanges != null) {
      _gameState.updateStats(choice.statChanges!);
    }

    // Mark current scene as visited
    _gameState.visitScene(_gameState.currentSceneId);

    // Move to next scene
    if (choice.nextSceneId != null) {
      await goToScene(choice.nextSceneId!);
    }

    // Auto save if enabled
    if (_autoSave) {
      await saveGame();
    }
  }

  // Go to specific scene
  Future<void> goToScene(String sceneId) async {
    _gameState.currentSceneId = sceneId;

    // Update chapter if needed
    Scene? scene = GameData.getScene(sceneId);
    if (scene != null) {
      _gameState.currentChapterId = scene.chapterId;
    }

    await _loadCurrentScene();
  }

  // Continue to next scene (for scenes without choices)
  Future<void> continueToNextScene() async {
    if (_currentScene?.nextSceneId != null) {
      await goToScene(_currentScene!.nextSceneId!);
    }
  }

  // Save game
  Future<void> saveGame([String? slotName]) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String key = slotName ?? 'current_save';

      _gameState.lastSaved = DateTime.now();
      String gameStateJson = jsonEncode(_gameState.toJson());

      await prefs.setString(key, gameStateJson);
    } catch (e) {
      debugPrint('Error saving game: $e');
    }
  }

  // Load game
  Future<bool> loadGame([String? slotName]) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      String key = slotName ?? 'current_save';

      String? gameStateJson = prefs.getString(key);
      if (gameStateJson != null) {
        Map<String, dynamic> json = jsonDecode(gameStateJson);
        _gameState = GameState.fromJson(json);
        await _loadCurrentScene();
        return true;
      }
    } catch (e) {
      debugPrint('Error loading game: $e');
    }
    return false;
  }

  // Start new game
  Future<void> startNewGame() async {
    _gameState = GameState.newGame();
    await _loadCurrentScene();
  }

  // Settings
  Future<void> _loadSettings() async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      _musicVolume = prefs.getDouble('music_volume') ?? 0.7;
      _soundVolume = prefs.getDouble('sound_volume') ?? 0.8;
      _textSpeed = prefs.getInt('text_speed') ?? 50;
      _autoSave = prefs.getBool('auto_save') ?? true;
    } catch (e) {
      debugPrint('Error loading settings: $e');
    }
  }

  Future<void> updateSettings({
    double? musicVolume,
    double? soundVolume,
    int? textSpeed,
    bool? autoSave,
  }) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();

      if (musicVolume != null) {
        _musicVolume = musicVolume;
        await prefs.setDouble('music_volume', musicVolume);
        _audioService.setMusicVolume(musicVolume);
      }

      if (soundVolume != null) {
        _soundVolume = soundVolume;
        await prefs.setDouble('sound_volume', soundVolume);
        _audioService.setSoundVolume(soundVolume);
      }

      if (textSpeed != null) {
        _textSpeed = textSpeed;
        await prefs.setInt('text_speed', textSpeed);
      }

      if (autoSave != null) {
        _autoSave = autoSave;
        await prefs.setBool('auto_save', autoSave);
      }

      notifyListeners();
    } catch (e) {
      debugPrint('Error updating settings: $e');
    }
  }

  // Get available choices for current scene
  List<Choice> getAvailableChoices() {
    if (_currentScene?.choices == null) return [];

    return _currentScene!.choices!
        .where((choice) => _gameState.canShowChoice(choice.requirements))
        .toList();
  }

  // Check if scene has choices
  bool get hasChoices => getAvailableChoices().isNotEmpty;

  // Check if can continue to next scene
  bool get canContinue => !hasChoices && _currentScene?.nextSceneId != null;
}
