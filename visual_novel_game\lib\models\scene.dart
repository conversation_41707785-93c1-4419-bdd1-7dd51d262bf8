// نموذج الخيار
class Choice {
  final String id;
  final String text;
  final String? nextSceneId;
  final Map<String, int>? statChanges; // تغييرات النقاط
  final List<String>? requirements; // متطلبات لإظهار الخيار
  
  Choice({
    required this.id,
    required this.text,
    this.nextSceneId,
    this.statChanges,
    this.requirements,
  });
  
  factory Choice.fromJson(Map<String, dynamic> json) {
    return Choice(
      id: json['id'],
      text: json['text'],
      nextSceneId: json['nextSceneId'],
      statChanges: json['statChanges'] != null 
          ? Map<String, int>.from(json['statChanges'])
          : null,
      requirements: json['requirements'] != null
          ? List<String>.from(json['requirements'])
          : null,
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'nextSceneId': nextSceneId,
      'statChanges': statChanges,
      'requirements': requirements,
    };
  }
}

// نموذج المشهد
class Scene {
  final String id;
  final String chapterId;
  final String background;
  final String? music;
  final String? soundEffect;
  final String? characterId;
  final String? characterExpression;
  final String text;
  final String? speaker;
  final List<Choice>? choices;
  final String? nextSceneId; // للمشاهد بدون خيارات
  final Map<String, dynamic>? specialEffects;
  
  Scene({
    required this.id,
    required this.chapterId,
    required this.background,
    this.music,
    this.soundEffect,
    this.characterId,
    this.characterExpression,
    required this.text,
    this.speaker,
    this.choices,
    this.nextSceneId,
    this.specialEffects,
  });
  
  factory Scene.fromJson(Map<String, dynamic> json) {
    return Scene(
      id: json['id'],
      chapterId: json['chapterId'],
      background: json['background'],
      music: json['music'],
      soundEffect: json['soundEffect'],
      characterId: json['characterId'],
      characterExpression: json['characterExpression'],
      text: json['text'],
      speaker: json['speaker'],
      choices: json['choices'] != null
          ? (json['choices'] as List).map((c) => Choice.fromJson(c)).toList()
          : null,
      nextSceneId: json['nextSceneId'],
      specialEffects: json['specialEffects'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chapterId': chapterId,
      'background': background,
      'music': music,
      'soundEffect': soundEffect,
      'characterId': characterId,
      'characterExpression': characterExpression,
      'text': text,
      'speaker': speaker,
      'choices': choices?.map((c) => c.toJson()).toList(),
      'nextSceneId': nextSceneId,
      'specialEffects': specialEffects,
    };
  }
}

// نموذج الفصل
class Chapter {
  final String id;
  final String title;
  final String description;
  final List<String> sceneIds;
  final String firstSceneId;
  
  Chapter({
    required this.id,
    required this.title,
    required this.description,
    required this.sceneIds,
    required this.firstSceneId,
  });
  
  factory Chapter.fromJson(Map<String, dynamic> json) {
    return Chapter(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      sceneIds: List<String>.from(json['sceneIds']),
      firstSceneId: json['firstSceneId'],
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'sceneIds': sceneIds,
      'firstSceneId': firstSceneId,
    };
  }
}
