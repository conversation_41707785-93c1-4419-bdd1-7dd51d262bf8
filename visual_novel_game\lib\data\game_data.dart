import '../models/scene.dart';

class GameData {
  static final Map<String, Scene> _scenes = {};
  static final Map<String, Chapter> _chapters = {};

  // Initialize game data
  static void initialize() {
    _loadChapters();
    _loadScenes();
  }

  // Load chapters
  static void _loadChapters() {
    _chapters['chapter_1'] = Chapter(
      id: 'chapter_1',
      title: 'الفصل الأول: العودة إلى الجذور',
      description: 'بارق يعود إلى قسنطينة ويكتشف أسرار عائلته',
      sceneIds: [
        'scene_1_1', 'scene_1_2', 'scene_1_3', 'scene_1_4',
        'scene_1_5', 'scene_1_6', 'scene_1_7', 'scene_1_8'
      ],
      firstSceneId: 'scene_1_1',
    );
  }

  // Load scenes for Chapter 1
  static void _loadScenes() {
    // Scene 1-1: الوصول إلى قسنطينة
    _scenes['scene_1_1'] = Scene(
      id: 'scene_1_1',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/train_station.png',
      music: 'audio/music/main_theme.mp3',
      text: 'أتوقف أمام محطة القطار في قسنطينة، أتنفس الهواء البارد لشهر فبراير. رائحة المطر المختلطة بعبق التاريخ تملأ أنفي.',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'normal',
      choices: [
        Choice(
          id: 'choice_1_1_1',
          text: 'أشعر بالحنين إلى هذا المكان',
          nextSceneId: 'scene_1_2',
          statChanges: {'emotion': 5},
        ),
        Choice(
          id: 'choice_1_1_2',
          text: 'يجب أن أركز على مهمتي',
          nextSceneId: 'scene_1_2',
          statChanges: {'practical': 5},
        ),
        Choice(
          id: 'choice_1_1_3',
          text: 'أتساءل عما ينتظرني هنا',
          nextSceneId: 'scene_1_2',
          statChanges: {'curiosity': 5},
        ),
      ],
    );

    // Scene 1-2: في شوارع المدينة القديمة
    _scenes['scene_1_2'] = Scene(
      id: 'scene_1_2',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/old_streets.png',
      music: 'audio/music/main_theme.mp3',
      soundEffect: 'audio/sounds/footsteps.wav',
      text: 'أحمل حقيبتي وأسير في شوارع المدينة العتيقة. الجسور المعلقة تذكرني بطفولتي هنا. كان جدي يحكي لي قصصاً عن تاريخ هذه المدينة.',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'normal',
      choices: [
        Choice(
          id: 'choice_1_2_1',
          text: 'أتمنى لو كان جدي هنا معي',
          nextSceneId: 'scene_1_3',
          statChanges: {'emotion': 10},
        ),
        Choice(
          id: 'choice_1_2_2',
          text: 'سأكتشف أسرار هذه المدينة',
          nextSceneId: 'scene_1_3',
          statChanges: {'adventure': 10},
        ),
        Choice(
          id: 'choice_1_2_3',
          text: 'يجب أن أركز على ترميم المنزل',
          nextSceneId: 'scene_1_3',
          statChanges: {'practical': 5},
        ),
      ],
    );

    // Scene 1-3: أمام منزل الجد
    _scenes['scene_1_3'] = Scene(
      id: 'scene_1_3',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/grandfather_house_exterior.png',
      music: 'audio/music/mystery_theme.mp3',
      soundEffect: 'audio/sounds/wind.wav',
      text: 'أقف أمام المنزل الحجري القديم في حي سوق الغزل. البناء عثماني الطراز، بجدرانه الحجرية السميكة ونوافذه المقوسة.',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'surprised',
      choices: [
        Choice(
          id: 'choice_1_3_1',
          text: 'بسم الله (أدخل مباشرة)',
          nextSceneId: 'scene_1_4',
          statChanges: {'faith': 5},
        ),
        Choice(
          id: 'choice_1_3_2',
          text: 'أتوقف لحظة لأتذكر جدي',
          nextSceneId: 'scene_1_4',
          statChanges: {'emotion': 10},
        ),
        Choice(
          id: 'choice_1_3_3',
          text: 'أفحص المنزل من الخارج أولاً',
          nextSceneId: 'scene_1_4',
          statChanges: {'caution': 5},
        ),
      ],
    );

    // Scene 1-4: داخل المنزل
    _scenes['scene_1_4'] = Scene(
      id: 'scene_1_4',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/grandfather_house_interior.png',
      music: 'audio/music/mystery_theme.mp3',
      soundEffect: 'audio/sounds/door_creak.wav',
      text: 'أدخل إلى الداخل، والظلام يلفني. الرائحة... رائحة الخشب القديم والبخور. تذكرني بجدي وهو يصلي في الغرفة الكبيرة.',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'normal',
      choices: [
        Choice(
          id: 'choice_1_4_1',
          text: 'أبدأ بتنظيف المكان',
          nextSceneId: 'scene_1_5',
          statChanges: {'practical': 5},
        ),
        Choice(
          id: 'choice_1_4_2',
          text: 'أستكشف المنزل أولاً',
          nextSceneId: 'scene_1_5',
          statChanges: {'curiosity': 10},
        ),
        Choice(
          id: 'choice_1_4_3',
          text: 'أجلس وأتذكر الماضي',
          nextSceneId: 'scene_1_5',
          statChanges: {'emotion': 10},
        ),
      ],
    );

    // Scene 1-5: اكتشاف المكتبة السرية
    _scenes['scene_1_5'] = Scene(
      id: 'scene_1_5',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/secret_library.png',
      music: 'audio/music/mystery_theme.mp3',
      soundEffect: 'audio/sounds/mechanism_click.wav',
      text: 'في المكتبة، أجد رفوفاً مليئة بالكتب القديمة. فجأة، ألاحظ شيئاً غريباً. إحدى الرفوف تتحرك! أدفعها وأسمع صوت آلية قديمة. الرف ينزلق كاشفاً عن تجويف مخفي!',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'surprised',
      choices: [
        Choice(
          id: 'choice_1_5_1',
          text: 'أستكشف التجويف فوراً',
          nextSceneId: 'scene_1_6',
          statChanges: {'adventure': 15},
        ),
        Choice(
          id: 'choice_1_5_2',
          text: 'أتأكد من أن المكان آمن أولاً',
          nextSceneId: 'scene_1_6',
          statChanges: {'caution': 10},
        ),
        Choice(
          id: 'choice_1_5_3',
          text: 'أتردد... هل هذا صحيح؟',
          nextSceneId: 'scene_1_6',
          statChanges: {'doubt': 5},
        ),
      ],
    );

    // Scene 1-6: الصندوق والخريطة
    _scenes['scene_1_6'] = Scene(
      id: 'scene_1_6',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/secret_library.png',
      music: 'audio/music/adventure_theme.mp3',
      soundEffect: 'audio/sounds/treasure_open.wav',
      text: 'قلبي يخفق بقوة وأنا أخرج صندوقاً خشبياً مغطى بالغبار. بداخله: خريطة قديمة، مفتاح ذهبي، رسالة بخط جدي، وقطعة نقدية عثمانية!',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'surprised',
      nextSceneId: 'scene_1_7',
    );

    // Scene 1-7: رسالة الجد
    _scenes['scene_1_7'] = Scene(
      id: 'scene_1_7',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/secret_library.png',
      music: 'audio/music/sad_theme.mp3',
      text: 'أقرأ رسالة جدي: "إلى حفيدي العزيز بارق، عائلتنا ليست عادية. جدك الأكبر كان أميناً على كنز عثماني عظيم. هذه الخريطة تقودك إليه، لكن احذر..."',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'worried',
      choices: [
        Choice(
          id: 'choice_1_7_1',
          text: 'لا أصدق هذا!',
          nextSceneId: 'scene_1_8',
          statChanges: {'shock': 5},
        ),
        Choice(
          id: 'choice_1_7_2',
          text: 'أشعر بالفخر بعائلتي',
          nextSceneId: 'scene_1_8',
          statChanges: {'pride': 10},
        ),
        Choice(
          id: 'choice_1_7_3',
          text: 'أقرأ باقي الرسالة بعناية',
          nextSceneId: 'scene_1_8',
          statChanges: {'focus': 10},
        ),
      ],
    );

    // Scene 1-8: القرار المصيري
    _scenes['scene_1_8'] = Scene(
      id: 'scene_1_8',
      chapterId: 'chapter_1',
      background: 'assets/images/backgrounds/grandfather_house_interior.png',
      music: 'audio/music/tension_theme.mp3',
      text: 'الليل يحل، وأنا أقف أمام خيار صعب. هل أتجاهل كل هذا وأكمل حياتي العادية؟ أم أتبع خطى جدي وأكتشف هذا السر؟',
      speaker: 'بارق',
      characterId: 'bariq',
      characterExpression: 'determined',
      choices: [
        Choice(
          id: 'choice_1_8_1',
          text: 'سأبحث عن الكنز وأكمل مسيرة جدي',
          nextSceneId: 'scene_2_1', // بداية الفصل الثاني
          statChanges: {'adventure': 20, 'determination': 15},
        ),
        Choice(
          id: 'choice_1_8_2',
          text: 'سأدفن هذا السر وأعيش حياة عادية',
          nextSceneId: 'ending_early', // نهاية مبكرة
          statChanges: {'caution': 10},
        ),
        Choice(
          id: 'choice_1_8_3',
          text: 'سأفكر في الأمر أكثر',
          nextSceneId: 'scene_1_8', // يعيد نفس المشهد
          statChanges: {'doubt': 5},
        ),
      ],
    );
  }

  // Get scene by ID
  static Scene? getScene(String sceneId) {
    return _scenes[sceneId];
  }

  // Get chapter by ID
  static Chapter? getChapter(String chapterId) {
    return _chapters[chapterId];
  }

  // Get all scenes for a chapter
  static List<Scene> getScenesForChapter(String chapterId) {
    return _scenes.values
        .where((scene) => scene.chapterId == chapterId)
        .toList();
  }
}
