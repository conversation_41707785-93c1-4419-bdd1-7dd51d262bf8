// نموذج حالة اللعبة
class GameState {
  String currentSceneId;
  String currentChapterId;
  Map<String, int> stats;
  Map<String, bool> flags;
  List<String> visitedScenes;
  DateTime lastSaved;
  
  GameState({
    required this.currentSceneId,
    required this.currentChapterId,
    Map<String, int>? stats,
    Map<String, bool>? flags,
    List<String>? visitedScenes,
    DateTime? lastSaved,
  }) : stats = stats ?? _getDefaultStats(),
       flags = flags ?? {},
       visitedScenes = visitedScenes ?? [],
       lastSaved = lastSaved ?? DateTime.now();
  
  static Map<String, int> _getDefaultStats() {
    return {
      'relationship_layla': 50,
      'courage': 50,
      'wisdom': 50,
      'emotion': 50,
      'adventure': 50,
      'practical': 50,
    };
  }
  
  // تحديث النقاط
  void updateStats(Map<String, int> changes) {
    changes.forEach((key, value) {
      stats[key] = (stats[key] ?? 0) + value;
      // التأكد من أن النقاط بين 0 و 100
      stats[key] = stats[key]!.clamp(0, 100);
    });
  }
  
  // تحديث العلامات
  void setFlag(String flag, bool value) {
    flags[flag] = value;
  }
  
  bool getFlag(String flag) {
    return flags[flag] ?? false;
  }
  
  // إضافة مشهد للمشاهد المزارة
  void visitScene(String sceneId) {
    if (!visitedScenes.contains(sceneId)) {
      visitedScenes.add(sceneId);
    }
  }
  
  // التحقق من متطلبات الخيار
  bool canShowChoice(List<String>? requirements) {
    if (requirements == null || requirements.isEmpty) return true;
    
    for (String requirement in requirements) {
      if (requirement.startsWith('stat_')) {
        // مثال: stat_courage_60 (يتطلب شجاعة 60 أو أكثر)
        List<String> parts = requirement.split('_');
        if (parts.length >= 3) {
          String statName = parts[1];
          int requiredValue = int.tryParse(parts[2]) ?? 0;
          if ((stats[statName] ?? 0) < requiredValue) return false;
        }
      } else if (requirement.startsWith('flag_')) {
        // مثال: flag_found_map (يتطلب أن تكون العلامة true)
        String flagName = requirement.substring(5);
        if (!getFlag(flagName)) return false;
      } else if (requirement.startsWith('visited_')) {
        // مثال: visited_scene_1_1 (يتطلب زيارة مشهد معين)
        String sceneId = requirement.substring(8);
        if (!visitedScenes.contains(sceneId)) return false;
      }
    }
    
    return true;
  }
  
  factory GameState.fromJson(Map<String, dynamic> json) {
    return GameState(
      currentSceneId: json['currentSceneId'],
      currentChapterId: json['currentChapterId'],
      stats: Map<String, int>.from(json['stats'] ?? {}),
      flags: Map<String, bool>.from(json['flags'] ?? {}),
      visitedScenes: List<String>.from(json['visitedScenes'] ?? []),
      lastSaved: DateTime.parse(json['lastSaved']),
    );
  }
  
  Map<String, dynamic> toJson() {
    return {
      'currentSceneId': currentSceneId,
      'currentChapterId': currentChapterId,
      'stats': stats,
      'flags': flags,
      'visitedScenes': visitedScenes,
      'lastSaved': lastSaved.toIso8601String(),
    };
  }
  
  // إنشاء حالة جديدة
  static GameState newGame() {
    return GameState(
      currentSceneId: 'scene_1_1',
      currentChapterId: 'chapter_1',
    );
  }
  
  // نسخ الحالة
  GameState copy() {
    return GameState(
      currentSceneId: currentSceneId,
      currentChapterId: currentChapterId,
      stats: Map<String, int>.from(stats),
      flags: Map<String, bool>.from(flags),
      visitedScenes: List<String>.from(visitedScenes),
      lastSaved: lastSaved,
    );
  }
}
