import 'package:audioplayers/audioplayers.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();
  
  final AudioPlayer _musicPlayer = AudioPlayer();
  final AudioPlayer _soundPlayer = AudioPlayer();
  
  double _musicVolume = 0.7;
  double _soundVolume = 0.8;
  
  String? _currentMusic;
  
  // Initialize audio service
  Future<void> initialize() async {
    await _musicPlayer.setReleaseMode(ReleaseMode.loop);
    await _soundPlayer.setReleaseMode(ReleaseMode.stop);
  }
  
  // Play background music
  Future<void> playMusic(String musicPath, {double? volume}) async {
    try {
      // Don't restart the same music
      if (_currentMusic == musicPath) return;
      
      double vol = volume ?? _musicVolume;
      await _musicPlayer.setVolume(vol);
      await _musicPlayer.play(AssetSource(musicPath));
      _currentMusic = musicPath;
    } catch (e) {
      print('Error playing music: $e');
    }
  }
  
  // Stop music
  Future<void> stopMusic() async {
    try {
      await _musicPlayer.stop();
      _currentMusic = null;
    } catch (e) {
      print('Error stopping music: $e');
    }
  }
  
  // Pause music
  Future<void> pauseMusic() async {
    try {
      await _musicPlayer.pause();
    } catch (e) {
      print('Error pausing music: $e');
    }
  }
  
  // Resume music
  Future<void> resumeMusic() async {
    try {
      await _musicPlayer.resume();
    } catch (e) {
      print('Error resuming music: $e');
    }
  }
  
  // Play sound effect
  Future<void> playSound(String soundPath, {double? volume}) async {
    try {
      double vol = volume ?? _soundVolume;
      await _soundPlayer.setVolume(vol);
      await _soundPlayer.play(AssetSource(soundPath));
    } catch (e) {
      print('Error playing sound: $e');
    }
  }
  
  // Set music volume
  Future<void> setMusicVolume(double volume) async {
    _musicVolume = volume.clamp(0.0, 1.0);
    await _musicPlayer.setVolume(_musicVolume);
  }
  
  // Set sound volume
  Future<void> setSoundVolume(double volume) async {
    _soundVolume = volume.clamp(0.0, 1.0);
    await _soundPlayer.setVolume(_soundVolume);
  }
  
  // Get current volumes
  double get musicVolume => _musicVolume;
  double get soundVolume => _soundVolume;
  
  // Dispose
  Future<void> dispose() async {
    await _musicPlayer.dispose();
    await _soundPlayer.dispose();
  }
}
