# أسرار الجزائر العتيقة - Visual Novel Game

## وصف اللعبة

لعبة رواية مرئية تفاعلية تحكي قصة بارق، مهندس معماري شاب يعود إلى مدينة قسنطينة لاكتشاف أسرار عائلته العريقة. اللعبة مصممة باستخدام Flutter وتستهدف منصة Android.

## المميزات المنجزة

### ✅ الهيكل الأساسي
- [x] إعداد مشروع Flutter كامل
- [x] نظام إدارة الحالة باستخدام Provider
- [x] هيكل ملفات منظم ومرتب
- [x] نماذج البيانات (Models) للشخصيات والمشاهد
- [x] خدمة الصوت والموسيقى

### ✅ الواجهات والشاشات
- [x] الشاشة الرئيسية مع قائمة hamburger
- [x] شاشة اللعبة الرئيسية
- [x] شاشة الإعدادات مع تحكم في الصوت وسرعة النص
- [x] شاشة "حول اللعبة" مع معلومات المطور

### ✅ نظام اللعبة
- [x] نظام المشاهد والحوارات
- [x] نظام الخيارات التفاعلية
- [x] نظام النقاط والعلاقات
- [x] نظام الحفظ والتحميل
- [x] انيميشن النصوص
- [x] تأثيرات بصرية وانتقالات

### ✅ المحتوى
- [x] الفصل الأول كاملاً (8 مشاهد)
- [x] 5 شخصيات رئيسية مع تعبيرات مختلفة
- [x] نظام الخيارات المتفرعة
- [x] قصة مشوقة بالثقافة الجزائرية

### ✅ التصميم
- [x] تصميم عربي أنيق مع خطوط مناسبة
- [x] ألوان متدرجة جميلة
- [x] واجهة مستخدم سهلة الاستخدام
- [x] تصميم متجاوب لمختلف الشاشات

## الملفات الرئيسية

```
lib/
├── main.dart                 # نقطة البداية
├── models/                   # نماذج البيانات
│   ├── character.dart        # نموذج الشخصيات
│   ├── scene.dart           # نموذج المشاهد والخيارات
│   └── game_state.dart      # حالة اللعبة
├── providers/               # إدارة الحالة
│   └── game_provider.dart   # مزود حالة اللعبة
├── screens/                 # الشاشات
│   ├── main_menu_screen.dart
│   ├── game_screen.dart
│   ├── settings_screen.dart
│   └── about_screen.dart
├── widgets/                 # المكونات المخصصة
│   ├── character_widget.dart
│   ├── text_box_widget.dart
│   └── choice_widget.dart
├── services/               # الخدمات
│   └── audio_service.dart  # خدمة الصوت
└── data/                   # بيانات اللعبة
    └── game_data.dart      # مشاهد وفصول اللعبة
```

## المكتبات المستخدمة

- **flutter**: إطار العمل الأساسي
- **provider**: إدارة الحالة
- **shared_preferences**: حفظ البيانات محلياً
- **audioplayers**: تشغيل الصوت والموسيقى
- **flutter_animate**: الانيميشن والتأثيرات
- **google_fonts**: الخطوط العربية
- **flutter_screenutil**: التصميم المتجاوب
- **flutter_staggered_animations**: انيميشن متقدم

## كيفية التشغيل

### المتطلبات
- Flutter SDK (3.7.0 أو أحدث)
- Android Studio أو VS Code
- جهاز Android أو محاكي

### خطوات التشغيل

1. **تحميل المكتبات**
```bash
flutter pub get
```

2. **تشغيل اللعبة**
```bash
# للتشغيل على Android
flutter run

# للتشغيل على الويب (للاختبار)
flutter run -d chrome
```

3. **بناء APK للإنتاج**
```bash
flutter build apk --release
```

## المطور

**شايبي وائل** - 2025
جميع الحقوق محفوظة

## الإصدار

**1.0.0** - الإصدار الأول

---

تم إنجاز هذا المشروع باستخدام **Ultrathink** و **Claude Sonnet 4** مع **Augment Code**.
