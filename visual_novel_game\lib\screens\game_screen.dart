import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/game_provider.dart';
import '../models/scene.dart';
import '../models/character.dart';
import '../widgets/character_widget.dart';
import '../widgets/text_box_widget.dart';
import '../widgets/choice_widget.dart';
import 'main_menu_screen.dart';

class GameScreen extends StatefulWidget {
  const GameScreen({super.key});

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Consumer<GameProvider>(
        builder: (context, gameProvider, child) {
          if (gameProvider.isLoading) {
            return const Center(
              child: CircularProgressIndicator(color: Colors.amber),
            );
          }

          final scene = gameProvider.currentScene;
          if (scene == null) {
            return const Center(
              child: Text(
                'خطأ في تحميل المشهد',
                style: TextStyle(color: Colors.white, fontSize: 18),
              ),
            );
          }

          return Stack(
            children: [
              // Background
              _buildBackground(scene),

              // Character
              if (scene.characterId != null)
                _buildCharacter(scene),

              // UI Overlay
              _buildUIOverlay(context, gameProvider, scene),

              // Text box
              _buildTextBox(context, gameProvider, scene),

              // Choices
              if (gameProvider.hasChoices && !gameProvider.isTextAnimating)
                _buildChoices(context, gameProvider),
            ],
          );
        },
      ),
    );
  }

  Widget _buildBackground(Scene scene) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage(scene.background),
          fit: BoxFit.cover,
          onError: (exception, stackTrace) {
            // Fallback to gradient background if image not found
          },
        ),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Colors.black.withValues(alpha: 0.3),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCharacter(Scene scene) {
    final character = GameCharacters.getAllCharacters()[scene.characterId];
    if (character == null) return const SizedBox.shrink();

    return Positioned(
      bottom: 200.h,
      left: 0,
      right: 0,
      child: CharacterWidget(
        character: character,
        expression: scene.characterExpression ?? character.defaultExpression,
      ),
    );
  }

  Widget _buildUIOverlay(BuildContext context, GameProvider gameProvider, Scene scene) {
    return Positioned(
      top: 0,
      left: 0,
      right: 0,
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Row(
            children: [
              // Menu button
              IconButton(
                onPressed: () => _showGameMenu(context),
                icon: Icon(
                  Icons.menu,
                  color: Colors.white,
                  size: 28.sp,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: Colors.black.withValues(alpha: 0.5),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                ),
              ),

              const Spacer(),

              // Auto/Skip buttons
              Row(
                children: [
                  IconButton(
                    onPressed: gameProvider.isTextAnimating
                        ? gameProvider.skipTextAnimation
                        : null,
                    icon: Icon(
                      Icons.fast_forward,
                      color: gameProvider.isTextAnimating
                          ? Colors.white
                          : Colors.white38,
                      size: 24.sp,
                    ),
                    style: IconButton.styleFrom(
                      backgroundColor: Colors.black.withValues(alpha: 0.5),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTextBox(BuildContext context, GameProvider gameProvider, Scene scene) {
    return Positioned(
      bottom: 0,
      left: 0,
      right: 0,
      child: TextBoxWidget(
        speaker: scene.speaker,
        text: gameProvider.displayedText,
        isAnimating: gameProvider.isTextAnimating,
        onTap: () {
          if (gameProvider.isTextAnimating) {
            gameProvider.skipTextAnimation();
          } else if (gameProvider.canContinue) {
            gameProvider.continueToNextScene();
          }
        },
      ),
    );
  }

  Widget _buildChoices(BuildContext context, GameProvider gameProvider) {
    final choices = gameProvider.getAvailableChoices();

    return Positioned(
      bottom: 200.h,
      left: 20.w,
      right: 20.w,
      child: Column(
        children: choices.map((choice) =>
          Padding(
            padding: EdgeInsets.only(bottom: 12.h),
            child: ChoiceWidget(
              choice: choice,
              onTap: () => gameProvider.makeChoice(choice),
            ),
          ),
        ).toList(),
      ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.3),
    );
  }

  void _showGameMenu(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: const Color(0xFF16213e),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.r),
        ),
        title: const Text(
          'قائمة اللعبة',
          style: TextStyle(color: Colors.white),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildMenuOption(
              context,
              'حفظ اللعبة',
              Icons.save,
              () {
                context.read<GameProvider>().saveGame();
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('تم حفظ اللعبة'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
            ),

            SizedBox(height: 12.h),

            _buildMenuOption(
              context,
              'الإعدادات',
              Icons.settings,
              () {
                Navigator.pop(context);
                // Navigate to settings
              },
            ),

            SizedBox(height: 12.h),

            _buildMenuOption(
              context,
              'العودة للقائمة الرئيسية',
              Icons.home,
              () {
                Navigator.pop(context);
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(builder: (context) => const MainMenuScreen()),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMenuOption(
    BuildContext context,
    String text,
    IconData icon,
    VoidCallback onTap,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: onTap,
        icon: Icon(icon, size: 20.sp),
        label: Text(
          text,
          style: TextStyle(fontSize: 14.sp),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF0f3460),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.r),
          ),
          padding: EdgeInsets.symmetric(vertical: 12.h),
        ),
      ),
    );
  }
}
