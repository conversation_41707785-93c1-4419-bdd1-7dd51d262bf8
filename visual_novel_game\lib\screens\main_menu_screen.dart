import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../providers/game_provider.dart';
import 'game_screen.dart';
import 'settings_screen.dart';
import 'about_screen.dart';

class MainMenuScreen extends StatefulWidget {
  const MainMenuScreen({super.key});

  @override
  State<MainMenuScreen> createState() => _MainMenuScreenState();
}

class _MainMenuScreenState extends State<MainMenuScreen> {
  @override
  void initState() {
    super.initState();
    // Initialize game provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<GameProvider>().initializeGame();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1a1a2e),
              Color(0xFF16213e),
              Color(0xFF0f3460),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header with hamburger menu
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Row(
                  children: [
                    IconButton(
                      onPressed: () => _showDrawer(context),
                      icon: Icon(
                        Icons.menu,
                        color: Colors.white,
                        size: 28.sp,
                      ),
                    ),
                    const Spacer(),
                  ],
                ),
              ),

              // Game title
              Expanded(
                flex: 2,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'أسرار الجزائر العتيقة',
                      style: TextStyle(
                        fontSize: 32.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.amber,
                        shadows: [
                          Shadow(
                            offset: const Offset(2, 2),
                            blurRadius: 4,
                            color: Colors.black.withValues(alpha: 0.5),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ).animate().fadeIn(duration: 1000.ms).slideY(begin: -0.3),

                    SizedBox(height: 16.h),

                    Text(
                      'رواية مرئية تفاعلية',
                      style: TextStyle(
                        fontSize: 18.sp,
                        color: Colors.white70,
                        fontStyle: FontStyle.italic,
                      ),
                      textAlign: TextAlign.center,
                    ).animate().fadeIn(delay: 500.ms, duration: 1000.ms),
                  ],
                ),
              ),

              // Menu buttons
              Expanded(
                flex: 3,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 40.w),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      _buildMenuButton(
                        context,
                        'بدء جديد',
                        Icons.play_arrow,
                        () => _startNewGame(context),
                        delay: 800,
                      ),

                      SizedBox(height: 20.h),

                      _buildMenuButton(
                        context,
                        'متابعة',
                        Icons.play_circle_outline,
                        () => _continueGame(context),
                        delay: 1000,
                      ),

                      SizedBox(height: 20.h),

                      _buildMenuButton(
                        context,
                        'الإعدادات',
                        Icons.settings,
                        () => _openSettings(context),
                        delay: 1200,
                      ),

                      SizedBox(height: 20.h),

                      _buildMenuButton(
                        context,
                        'حول اللعبة',
                        Icons.info_outline,
                        () => _openAbout(context),
                        delay: 1400,
                      ),
                    ],
                  ),
                ),
              ),

              // Footer
              Padding(
                padding: EdgeInsets.all(16.w),
                child: Text(
                  'تطوير: شايبي وائل © 2025',
                  style: TextStyle(
                    fontSize: 12.sp,
                    color: Colors.white38,
                  ),
                  textAlign: TextAlign.center,
                ).animate().fadeIn(delay: 1600.ms, duration: 1000.ms),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuButton(
    BuildContext context,
    String text,
    IconData icon,
    VoidCallback onPressed,
    {int delay = 0}
  ) {
    return SizedBox(
      width: double.infinity,
      height: 60.h,
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: 24.sp),
        label: Text(
          text,
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: const Color(0xFF0f3460).withValues(alpha: 0.8),
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.r),
            side: BorderSide(
              color: Colors.amber.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          elevation: 8,
          shadowColor: Colors.amber.withValues(alpha: 0.3),
        ),
      ),
    ).animate().fadeIn(delay: delay.ms, duration: 800.ms).slideX(begin: -0.3);
  }

  Future<void> _startNewGame(BuildContext context) async {
    final navigator = Navigator.of(context);
    final gameProvider = context.read<GameProvider>();
    await gameProvider.startNewGame();
    if (mounted) {
      navigator.pushReplacement(
        MaterialPageRoute(builder: (context) => const GameScreen()),
      );
    }
  }

  Future<void> _continueGame(BuildContext context) async {
    final navigator = Navigator.of(context);
    final messenger = ScaffoldMessenger.of(context);
    final gameProvider = context.read<GameProvider>();
    final success = await gameProvider.loadGame();
    if (mounted) {
      if (success) {
        navigator.pushReplacement(
          MaterialPageRoute(builder: (context) => const GameScreen()),
        );
      } else {
        messenger.showSnackBar(
          const SnackBar(
            content: Text('لا توجد لعبة محفوظة'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _openSettings(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  void _openAbout(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(builder: (context) => const AboutScreen()),
    );
  }

  void _showDrawer(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: const Color(0xFF16213e),
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20.r),
            topRight: Radius.circular(20.r),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 40.w,
              height: 4.h,
              margin: EdgeInsets.symmetric(vertical: 12.h),
              decoration: BoxDecoration(
                color: Colors.white38,
                borderRadius: BorderRadius.circular(2.r),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.settings, color: Colors.white),
              title: const Text('الإعدادات', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _openSettings(context);
              },
            ),
            ListTile(
              leading: const Icon(Icons.info_outline, color: Colors.white),
              title: const Text('حول اللعبة', style: TextStyle(color: Colors.white)),
              onTap: () {
                Navigator.pop(context);
                _openAbout(context);
              },
            ),
            SizedBox(height: 20.h),
          ],
        ),
      ),
    );
  }
}
